#!/usr/bin/env python3
"""
修正后的点云配准测试程序
测试修正后的算法逻辑
"""

import open3d as o3d
import numpy as np
import copy
import matplotlib.pyplot as plt
import os
import sys

# 导入修正后的函数
from srs_profile_register_format_xyz import icp_registration, distance_near_point_sick, save_plt
from srs_stitching_error import calculate_stitching_error

def test_registration_with_existing_matrix():
    """
    使用现有的变换矩阵测试配准效果
    """
    print("=== 测试修正后的配准算法 ===")
    
    # 测试参数
    file_idx = 1  # 使用第一组数据
    color_str = 'blue'
    
    # 文件路径
    path = f"./20250730/{file_idx}/"
    source_filename = path + f'{color_str}_filtered.xyz'
    model_filename = 'rail-3.xyz'
    matrix_filename = path + f'{color_str}_filtered_init_trans_matrix.txt'
    
    print(f"加载源点云: {source_filename}")
    print(f"加载目标模板: {model_filename}")
    print(f"加载变换矩阵: {matrix_filename}")
    
    # 加载点云数据
    try:
        source_points = np.loadtxt(source_filename)
        target_points = np.loadtxt(model_filename)
        trans_init = np.loadtxt(matrix_filename)
        
        print(f"源点云点数: {len(source_points)}")
        print(f"目标点云点数: {len(target_points)}")
        
    except Exception as e:
        print(f"加载数据失败: {e}")
        return
    
    # 创建点云对象
    source = o3d.geometry.PointCloud()
    source.points = o3d.utility.Vector3dVector(source_points)
    
    target = o3d.geometry.PointCloud()
    target.points = o3d.utility.Vector3dVector(target_points)
    
    # 设置颜色
    source.paint_uniform_color([1, 0, 0])  # 红色
    target.paint_uniform_color([0, 1, 0])  # 绿色
    
    print("\n=== 初始配准测试 ===")
    source_init = copy.deepcopy(source)
    source_init.transform(trans_init)
    print("初始配准完成")
    
    # 可视化初始配准结果
    print("显示初始配准结果...")
    # o3d.visualization.draw_geometries([source_init, target], window_name="初始配准结果")
    
    print("\n=== ICP精细配准测试 ===")
    # 使用修正后的ICP参数
    trans_icp = icp_registration(source, target, trans_init, max_correspondence_distance=0.5)
    
    source_final = copy.deepcopy(source)
    source_final.transform(trans_icp)
    print("ICP配准完成")
    
    # 可视化最终配准结果
    print("显示最终配准结果...")
    # o3d.visualization.draw_geometries([source_final, target], window_name="ICP配准结果")
    
    print("\n=== 配准精度分析 ===")
    # 使用修正后的距离分析函数
    save_plt_name = source_filename.replace('.xyz', '_corrected_result.jpg')
    distance_near_point_sick(target, source_final, save_plt_name)
    
    print(f"配准结果图已保存: {save_plt_name}")
    
    return source_final, target, trans_icp

def test_stitching_error_analysis():
    """
    测试修正后的拼接误差分析
    """
    print("\n=== 测试修正后的拼接误差分析 ===")
    
    file_index = 1
    reference_filename = f"./20250730/{file_index}/blue_filtered.xyz"
    target_filename = f"./20250730/{file_index}/green_filtered.xyz"
    
    # 生成输出文件名
    base_path = os.path.dirname(reference_filename)
    ref_name = os.path.splitext(os.path.basename(reference_filename))[0]
    tar_name = os.path.splitext(os.path.basename(target_filename))[0]
    output_filename = os.path.join(base_path, f"stitching_error_{ref_name}_vs_{tar_name}_corrected.txt")
    
    try:
        # 加载点云
        reference_points = np.loadtxt(reference_filename)
        target_points = np.loadtxt(target_filename)
        
        print(f"参考点云: {reference_filename} ({len(reference_points)} 点)")
        print(f"目标点云: {target_filename} ({len(target_points)} 点)")
        
        # 动态确定Z值范围
        ref_z_min, ref_z_max = np.min(reference_points[:, 2]), np.max(reference_points[:, 2])
        tar_z_min, tar_z_max = np.min(target_points[:, 2]), np.max(target_points[:, 2])
        
        z_min = max(ref_z_min, tar_z_min)
        z_max = min(ref_z_max, tar_z_max)
        
        print(f"参考点云Z范围: [{ref_z_min:.2f}, {ref_z_max:.2f}]")
        print(f"目标点云Z范围: [{tar_z_min:.2f}, {tar_z_max:.2f}]")
        print(f"使用重叠Z范围: [{z_min:.2f}, {z_max:.2f}]")
        
        # 过滤点云
        z_mask_ref = (reference_points[:, 2] >= z_min) & (reference_points[:, 2] <= z_max)
        reference_points_filtered = reference_points[z_mask_ref]
        
        z_mask_tar = (target_points[:, 2] >= z_min) & (target_points[:, 2] <= z_max)
        target_points_filtered = target_points[z_mask_tar]
        
        print(f"过滤后参考点云: {len(reference_points_filtered)} 点")
        print(f"过滤后目标点云: {len(target_points_filtered)} 点")
        
        # 创建点云对象
        reference_pcd = o3d.geometry.PointCloud()
        reference_pcd.points = o3d.utility.Vector3dVector(reference_points_filtered)
        
        target_pcd = o3d.geometry.PointCloud()
        target_pcd.points = o3d.utility.Vector3dVector(target_points_filtered)
        
        # 计算拼接误差
        distance_th = 1.0
        error_stats = calculate_stitching_error(reference_pcd, target_pcd, 
                                              distance_threshold=distance_th, 
                                              output_file=output_filename)
        
        print(f"误差分析结果已保存: {output_filename}")
        return error_stats
        
    except Exception as e:
        print(f"拼接误差分析失败: {e}")
        return None

def compare_results():
    """
    比较修正前后的结果
    """
    print("\n=== 比较修正前后的结果 ===")
    
    file_index = 1
    
    # 原始结果文件
    original_file = f"./20250730/{file_index}/stitching_error_blue_filtered_vs_green_filtered.txt"
    
    # 修正后结果文件
    corrected_file = f"./20250730/{file_index}/stitching_error_blue_filtered_vs_green_filtered_corrected.txt"
    
    try:
        if os.path.exists(original_file):
            print("原始结果:")
            with open(original_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                for line in lines[7:17]:  # 显示关键统计信息
                    print(f"  {line.strip()}")
        
        if os.path.exists(corrected_file):
            print("\n修正后结果:")
            with open(corrected_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                for line in lines[7:17]:  # 显示关键统计信息
                    print(f"  {line.strip()}")
                    
    except Exception as e:
        print(f"比较结果失败: {e}")

def main():
    """
    主测试函数
    """
    print("开始测试修正后的点云配准系统...")
    
    # 测试1: 配准算法
    try:
        source_final, target, trans_icp = test_registration_with_existing_matrix()
        print("✓ 配准算法测试完成")
    except Exception as e:
        print(f"✗ 配准算法测试失败: {e}")
    
    # 测试2: 拼接误差分析
    try:
        error_stats = test_stitching_error_analysis()
        print("✓ 拼接误差分析测试完成")
    except Exception as e:
        print(f"✗ 拼接误差分析测试失败: {e}")
    
    # 测试3: 结果比较
    try:
        compare_results()
        print("✓ 结果比较完成")
    except Exception as e:
        print(f"✗ 结果比较失败: {e}")
    
    print("\n所有测试完成!")

if __name__ == "__main__":
    main()
