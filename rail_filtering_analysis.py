#!/usr/bin/env python3
"""
Rail数据滤波处理分析报告
"""

import numpy as np
import matplotlib.pyplot as plt
import os

def analyze_filtering_process():
    """
    分析rail数据的滤波处理过程
    """
    print("=" * 70)
    print("Rail数据滤波处理分析报告")
    print("=" * 70)
    
    print("\n📊 原始数据概览")
    print("-" * 50)
    
    # 原始数据统计
    original_data = {
        'blue': {'points': 420952, 'z_range': [86.76, 207.11]},
        'green': {'points': 2860676, 'z_range': [63.28, 206.87]},
        'red': {'points': 2988653, 'z_range': [-267.58, 206.51]}
    }
    
    total_original = sum([data['points'] for data in original_data.values()])
    
    for color, data in original_data.items():
        z_min, z_max = data['z_range']
        z_span = z_max - z_min
        print(f"{color.upper()}点云:")
        print(f"  原始点数: {data['points']:,}")
        print(f"  Z值范围: [{z_min:.2f}, {z_max:.2f}] mm")
        print(f"  Z值跨度: {z_span:.2f} mm")
    
    print(f"\n总原始点数: {total_original:,}")
    
    print("\n🔍 滤波处理步骤")
    print("-" * 50)
    
    print("1. 数据格式处理:")
    print("   - 检查数据列数，如果超过3列则只取前3列(XYZ坐标)")
    print("   - 确保数据格式统一")
    
    print("\n2. Z值范围滤波:")
    print("   - 计算各点云的Z值范围")
    print("   - 确定公共Z值范围: [86.76, 206.51] mm")
    print("   - 只保留在公共范围内的点")
    
    # 公共Z范围
    common_z_min = 86.76
    common_z_max = 206.51
    common_z_span = common_z_max - common_z_min
    
    print(f"   公共Z范围: [{common_z_min:.2f}, {common_z_max:.2f}] mm")
    print(f"   公共Z跨度: {common_z_span:.2f} mm")
    
    print("\n3. Z值滤波效果:")
    filtered_data = {
        'blue': {'original': 420952, 'filtered': 418120, 'z_loss': 2832},
        'green': {'original': 2860676, 'filtered': 2697957, 'z_loss': 162719},
        'red': {'original': 2988653, 'filtered': 355628, 'z_loss': 2633025}
    }
    
    for color, data in filtered_data.items():
        loss_ratio = (data['z_loss'] / data['original']) * 100
        retain_ratio = (data['filtered'] / data['original']) * 100
        print(f"   {color.upper()}:")
        print(f"     滤波前: {data['original']:,} 点")
        print(f"     滤波后: {data['filtered']:,} 点")
        print(f"     损失: {data['z_loss']:,} 点 ({loss_ratio:.1f}%)")
        print(f"     保留: {retain_ratio:.1f}%")
    
    print("\n4. 随机采样处理:")
    print("   - 目标: 每个点云限制为50,000点")
    print("   - 方法: 随机采样(无替换)")
    print("   - 目的: 提高处理效率，减少计算时间")
    
    sampling_data = {
        'blue': {'filtered': 418120, 'sampled': 50000},
        'green': {'filtered': 2697957, 'sampled': 50000},
        'red': {'filtered': 355628, 'sampled': 50000}
    }
    
    for color, data in sampling_data.items():
        if data['filtered'] > 50000:
            sampling_ratio = (data['sampled'] / data['filtered']) * 100
            print(f"   {color.upper()}: {data['filtered']:,} → {data['sampled']:,} ({sampling_ratio:.1f}%)")
        else:
            print(f"   {color.upper()}: {data['filtered']:,} → {data['sampled']:,} (无需采样)")
    
    print("\n📈 滤波处理影响分析")
    print("-" * 50)
    
    print("1. Z值滤波影响:")
    print("   - BLUE: 损失最小(0.7%)，数据质量高")
    print("   - GREEN: 损失中等(5.7%)，主要是边界数据")
    print("   - RED: 损失最大(88.1%)，存在大量异常Z值")
    
    print("\n2. RED点云异常分析:")
    red_original_z_min = -267.58
    red_original_z_max = 206.51
    red_total_span = red_original_z_max - red_original_z_min
    red_valid_span = common_z_max - common_z_min
    
    print(f"   原始Z范围: [{red_original_z_min:.2f}, {red_original_z_max:.2f}] mm")
    print(f"   总跨度: {red_total_span:.2f} mm")
    print(f"   有效跨度: {red_valid_span:.2f} mm")
    print(f"   异常数据占比: {((red_total_span - red_valid_span) / red_total_span * 100):.1f}%")
    print("   → RED点云包含大量负Z值数据，可能是测量异常或坐标系问题")
    
    print("\n3. 随机采样影响:")
    print("   - 优点: 大幅提高处理速度，减少内存占用")
    print("   - 缺点: 可能丢失部分细节信息")
    print("   - 采样率: BLUE(12.0%), GREEN(1.9%), RED(14.1%)")
    
    print("\n🔧 滤波策略评估")
    print("-" * 50)
    
    print("当前滤波策略:")
    print("✓ Z值范围滤波: 有效去除异常数据")
    print("✓ 随机采样: 平衡处理效率和数据完整性")
    print("✓ 格式统一: 确保数据一致性")
    
    print("\n潜在改进:")
    print("• 统计滤波: 去除离群点")
    print("• 体素滤波: 均匀降采样")
    print("• 法向量滤波: 去除表面质量差的点")
    print("• 密度滤波: 去除稀疏区域的点")
    
    print("\n💡 滤波处理建议")
    print("-" * 50)
    
    print("1. 针对RED点云:")
    print("   - 检查数据采集过程，确认负Z值的来源")
    print("   - 考虑预处理步骤，修正坐标系偏移")
    print("   - 增加数据质量检查机制")
    
    print("2. 采样策略优化:")
    print("   - 考虑使用体素网格采样替代随机采样")
    print("   - 根据点云密度自适应调整采样率")
    print("   - 保留关键特征区域的高密度采样")
    
    print("3. 滤波参数调优:")
    print("   - 根据具体应用需求调整Z值范围")
    print("   - 添加X、Y方向的范围限制")
    print("   - 实现多级滤波策略")
    
    print("\n📊 滤波效果总结")
    print("-" * 50)
    
    total_filtered = sum([data['filtered'] for data in filtered_data.values()])
    total_sampled = 150000  # 3 * 50000
    
    print(f"数据处理流程:")
    print(f"  原始总点数: {total_original:,}")
    print(f"  Z值滤波后: {total_filtered:,} ({(total_filtered/total_original*100):.1f}%)")
    print(f"  随机采样后: {total_sampled:,} ({(total_sampled/total_original*100):.1f}%)")
    print(f"  数据压缩比: {(total_original/total_sampled):.1f}:1")
    
    print(f"\n处理效果:")
    print(f"  ✓ 去除了异常Z值数据")
    print(f"  ✓ 统一了数据格式和范围")
    print(f"  ✓ 大幅提高了处理效率")
    print(f"  ✓ 保持了数据的代表性")

def visualize_filtering_impact():
    """
    可视化滤波处理的影响
    """
    print("\n📈 生成滤波影响可视化图表...")
    
    # 数据准备
    colors = ['Blue', 'Green', 'Red']
    original = [420952, 2860676, 2988653]
    filtered = [418120, 2697957, 355628]
    sampled = [50000, 50000, 50000]
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 子图1: 数据量变化
    x = np.arange(len(colors))
    width = 0.25
    
    ax1.bar(x - width, original, width, label='原始数据', alpha=0.8)
    ax1.bar(x, filtered, width, label='Z值滤波后', alpha=0.8)
    ax1.bar(x + width, sampled, width, label='随机采样后', alpha=0.8)
    
    ax1.set_xlabel('点云类型')
    ax1.set_ylabel('点数')
    ax1.set_title('Rail数据滤波处理过程')
    ax1.set_xticks(x)
    ax1.set_xticklabels(colors)
    ax1.legend()
    ax1.set_yscale('log')  # 使用对数刻度
    
    # 添加数值标签
    for i, (orig, filt, samp) in enumerate(zip(original, filtered, sampled)):
        ax1.text(i - width, orig, f'{orig:,}', ha='center', va='bottom', fontsize=8)
        ax1.text(i, filt, f'{filt:,}', ha='center', va='bottom', fontsize=8)
        ax1.text(i + width, samp, f'{samp:,}', ha='center', va='bottom', fontsize=8)
    
    # 子图2: 数据保留率
    z_retain_rates = [(f/o)*100 for f, o in zip(filtered, original)]
    sample_rates = [(s/f)*100 for s, f in zip(sampled, filtered)]
    
    ax2.bar(x - width/2, z_retain_rates, width, label='Z值滤波保留率', alpha=0.8)
    ax2.bar(x + width/2, sample_rates, width, label='采样率', alpha=0.8)
    
    ax2.set_xlabel('点云类型')
    ax2.set_ylabel('保留率 (%)')
    ax2.set_title('数据保留率分析')
    ax2.set_xticks(x)
    ax2.set_xticklabels(colors)
    ax2.legend()
    
    # 添加数值标签
    for i, (z_rate, s_rate) in enumerate(zip(z_retain_rates, sample_rates)):
        ax2.text(i - width/2, z_rate, f'{z_rate:.1f}%', ha='center', va='bottom')
        ax2.text(i + width/2, s_rate, f'{s_rate:.1f}%', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('rail_filtering_analysis.png', dpi=300, bbox_inches='tight')
    print("  图表已保存: rail_filtering_analysis.png")
    
    plt.show()

def main():
    """
    主函数
    """
    analyze_filtering_process()
    
    try:
        visualize_filtering_impact()
    except Exception as e:
        print(f"可视化生成失败: {e}")
    
    print("\n" + "=" * 70)
    print("滤波处理分析完成")
    print("=" * 70)

if __name__ == "__main__":
    main()
