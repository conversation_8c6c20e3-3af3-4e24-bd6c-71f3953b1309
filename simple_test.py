#!/usr/bin/env python3
"""
简化的测试程序，测试修正后的算法
"""

import open3d as o3d
import numpy as np
import copy
import matplotlib.pyplot as plt
import os

def test_icp_registration():
    """
    测试修正后的ICP配准
    """
    print("=== 测试ICP配准 ===")
    
    # 文件路径
    file_idx = 1
    color_str = 'blue'
    path = f"./20250730/{file_idx}/"
    source_filename = path + f'{color_str}_filtered.xyz'
    model_filename = 'rail-3.xyz'
    matrix_filename = path + f'{color_str}_filtered_init_trans_matrix.txt'
    
    try:
        # 加载数据
        source_points = np.loadtxt(source_filename)
        target_points = np.loadtxt(model_filename)
        trans_init = np.loadtxt(matrix_filename)
        
        print(f"源点云点数: {len(source_points)}")
        print(f"目标点云点数: {len(target_points)}")
        
        # 创建点云对象
        source = o3d.geometry.PointCloud()
        source.points = o3d.utility.Vector3dVector(source_points)
        
        target = o3d.geometry.PointCloud()
        target.points = o3d.utility.Vector3dVector(target_points)
        
        # 修正后的ICP配准
        print("执行ICP配准...")
        reg_p2p = o3d.pipelines.registration.registration_icp(
            source, target, 0.5, trans_init,  # 使用0.5mm的对应距离阈值
            o3d.pipelines.registration.TransformationEstimationPointToPoint(),
            o3d.pipelines.registration.ICPConvergenceCriteria(
                relative_fitness=1e-4,
                relative_rmse=1e-4,
                max_iteration=100))
        
        print(f"ICP配准结果:")
        print(f"  Fitness: {reg_p2p.fitness:.6f}")
        print(f"  RMSE: {reg_p2p.inlier_rmse:.6f}")
        print(f"  对应点数: {len(reg_p2p.correspondence_set)}")
        
        # 应用变换
        source_transformed = copy.deepcopy(source)
        source_transformed.transform(reg_p2p.transformation)
        
        # 计算配准后的距离误差
        distances = source_transformed.compute_point_cloud_distance(target)
        distances = np.asarray(distances)
        
        # 筛选距离小于1.0mm的点
        close_points = distances[distances < 1.0]
        if len(close_points) > 0:
            print(f"距离分析:")
            print(f"  距离<1.0mm的点数: {len(close_points)}/{len(distances)} ({100*len(close_points)/len(distances):.1f}%)")
            print(f"  平均距离: {np.mean(close_points):.4f} mm")
            print(f"  最大距离: {np.max(close_points):.4f} mm")
            print(f"  距离<0.2mm的点数: {len(close_points[close_points < 0.2])}/{len(close_points)} ({100*len(close_points[close_points < 0.2])/len(close_points):.1f}%)")
        
        return True
        
    except Exception as e:
        print(f"ICP测试失败: {e}")
        return False

def test_stitching_error():
    """
    测试拼接误差分析
    """
    print("\n=== 测试拼接误差分析 ===")
    
    file_index = 1
    reference_filename = f"./20250730/{file_index}/blue_filtered.xyz"
    target_filename = f"./20250730/{file_index}/green_filtered.xyz"
    
    try:
        # 加载点云
        reference_points = np.loadtxt(reference_filename)
        target_points = np.loadtxt(target_filename)
        
        print(f"参考点云: {len(reference_points)} 点")
        print(f"目标点云: {len(target_points)} 点")
        
        # 动态确定Z值范围
        ref_z_min, ref_z_max = np.min(reference_points[:, 2]), np.max(reference_points[:, 2])
        tar_z_min, tar_z_max = np.min(target_points[:, 2]), np.max(target_points[:, 2])
        
        z_min = max(ref_z_min, tar_z_min)
        z_max = min(ref_z_max, tar_z_max)
        
        print(f"Z范围 - 参考: [{ref_z_min:.1f}, {ref_z_max:.1f}], 目标: [{tar_z_min:.1f}, {tar_z_max:.1f}]")
        print(f"重叠Z范围: [{z_min:.1f}, {z_max:.1f}]")
        
        # 过滤点云
        z_mask_ref = (reference_points[:, 2] >= z_min) & (reference_points[:, 2] <= z_max)
        reference_points_filtered = reference_points[z_mask_ref]
        
        z_mask_tar = (target_points[:, 2] >= z_min) & (target_points[:, 2] <= z_max)
        target_points_filtered = target_points[z_mask_tar]
        
        print(f"过滤后 - 参考: {len(reference_points_filtered)} 点, 目标: {len(target_points_filtered)} 点")
        
        # 创建点云对象
        reference_pcd = o3d.geometry.PointCloud()
        reference_pcd.points = o3d.utility.Vector3dVector(reference_points_filtered)
        
        target_pcd = o3d.geometry.PointCloud()
        target_pcd.points = o3d.utility.Vector3dVector(target_points_filtered)
        
        # 构建KD树进行最近邻搜索
        target_tree = o3d.geometry.KDTreeFlann(target_pcd)
        
        distances = []
        z_errors = []
        distance_threshold = 1.0
        
        # 计算每个参考点到目标点云的最近距离和Z误差
        for ref_point in reference_points_filtered:
            [k, idx, distance_squared] = target_tree.search_knn_vector_3d(ref_point, 1)
            
            if k > 0:
                distance = np.sqrt(distance_squared[0])
                if distance < distance_threshold:
                    target_point = target_points_filtered[idx[0]]
                    z_error = abs(ref_point[2] - target_point[2])
                    
                    distances.append(distance)
                    z_errors.append(z_error)
        
        if len(z_errors) > 0:
            z_errors = np.array(z_errors)
            distances = np.array(distances)
            
            print(f"拼接误差分析结果:")
            print(f"  匹配点对数: {len(z_errors)}")
            print(f"  Z方向误差:")
            print(f"    平均: {np.mean(z_errors):.4f} mm")
            print(f"    最大: {np.max(z_errors):.4f} mm")
            print(f"    最小: {np.min(z_errors):.4f} mm")
            print(f"    标准差: {np.std(z_errors):.4f} mm")
            print(f"  Z误差<0.3mm占比: {100*len(z_errors[z_errors < 0.3])/len(z_errors):.1f}%")
            print(f"  平均距离: {np.mean(distances):.4f} mm")
        else:
            print("没有找到匹配的点对")
        
        return True
        
    except Exception as e:
        print(f"拼接误差分析失败: {e}")
        return False

def main():
    """
    主测试函数
    """
    print("开始测试修正后的算法...")
    
    # 测试ICP配准
    icp_success = test_icp_registration()
    
    # 测试拼接误差分析
    stitching_success = test_stitching_error()
    
    print(f"\n测试结果:")
    print(f"  ICP配准: {'✓ 成功' if icp_success else '✗ 失败'}")
    print(f"  拼接误差分析: {'✓ 成功' if stitching_success else '✗ 失败'}")

if __name__ == "__main__":
    main()
